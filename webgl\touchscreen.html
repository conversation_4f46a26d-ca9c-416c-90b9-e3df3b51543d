<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 触摸屏版本</title>
    <link rel="shortcut icon" href="logo.png">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本样式
         * 专为1920×1080触摸屏优化设计
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
            user-select: none; /* 触摸屏优化：禁用文本选择 */
            -webkit-touch-callout: none; /* 禁用长按菜单 */
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* 主容器 */
        .touchscreen-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            z-index: 1;
        }

        /* 顶部标题栏 */
        .header {
            height: 120px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .system-title {
            font-size: 32px;
            font-weight: bold;
            color: var(--text-primary);
            text-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .time-display {
            font-size: 24px;
            color: var(--accent-color);
            font-weight: bold;
            text-shadow: 0 2px 10px rgba(0, 255, 136, 0.5);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 30px;
            gap: 30px;
        }

        /* 功能模块网格 */
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 30px;
            flex: 1;
        }

        /* 功能模块按钮 */
        .module-btn {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border: 2px solid var(--border-color);
            border-radius: 20px;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
            font-size: 24px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            /* 触摸屏优化 */
            min-height: 180px;
            touch-action: manipulation;
        }

        .module-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .module-btn:hover::before,
        .module-btn:active::before {
            left: 100%;
        }

        .module-btn:hover,
        .module-btn:active {
            border-color: var(--primary-color);
            box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
            transform: translateY(-5px) scale(1.02);
        }

        .module-btn:active {
            transform: translateY(-2px) scale(0.98);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.6);
        }

        .module-icon {
            font-size: 48px;
            color: var(--primary-color);
            text-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
        }

        .module-text {
            font-size: 20px;
            text-align: center;
            line-height: 1.2;
        }

        /* 底部状态栏 */
        .footer {
            height: 80px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-top: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            font-size: 18px;
        }

        .footer-left {
            color: var(--text-secondary);
        }

        .footer-right {
            color: var(--text-secondary);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            width: 1920px;
            height: 1080px;
            background: var(--bg-primary);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
        }

        .modal-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1001;
            pointer-events: none; /* 让背景区域不阻挡点击 */
        }

        .modal-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            height: 100%;
            pointer-events: auto; /* 恢复按钮的点击功能 */
        }

        .modal-controls .modal-btn:first-child {
            /* 返回按钮在左侧 */
            order: 1;
        }

        .modal-controls .modal-btn:last-child {
            /* 关闭按钮在右侧 */
            order: 2;
        }

        .modal-btn {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(42, 52, 65, 0.9), rgba(26, 35, 50, 0.9));
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s ease;
            touch-action: manipulation;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-btn:hover,
        .modal-btn:active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.2));
            border-color: var(--primary-color);
            color: var(--primary-color);
            box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
            transform: scale(1.05);
        }

        .modal-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 15px rgba(0, 212, 255, 0.6);
        }

        .modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-primary);
        }

        /* 响应式优化 */
        @media (max-width: 1920px) {
            body {
                width: 100vw;
                height: 100vh;
            }

            .modal-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="touchscreen-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="logo.png" alt="桂林智源" class="logo-image">
                    <span class="logo-text">桂林智源</span>
                </div>
                <div class="system-title">SVG 数字化系统 - 触摸屏版本</div>
            </div>
            <div class="header-right">
                <div class="time-display" id="currentTime"></div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 功能模块网格 -->
            <div class="modules-grid">
                <!-- 第一行 -->
                <button class="module-btn" onclick="openModule('electrical-topology')">
                    <i class="fas fa-bolt module-icon"></i>
                    <span class="module-text">电气拓扑</span>
                </button>

                <button class="module-btn" onclick="openModule('cooling-topology')">
                    <i class="fas fa-tint module-icon"></i>
                    <span class="module-text">水冷拓扑</span>
                </button>

                <button class="module-btn" onclick="openModule('io-status')">
                    <i class="fas fa-plug module-icon"></i>
                    <span class="module-text">I/O状态</span>
                </button>

                <button class="module-btn" onclick="openModule('unit-status')">
                    <i class="fas fa-microchip module-icon"></i>
                    <span class="module-text">单元状态</span>
                </button>

                <!-- 第二行 -->
                <button class="module-btn" onclick="openModule('master-control')">
                    <i class="fas fa-sitemap module-icon"></i>
                    <span class="module-text">主控辅控</span>
                </button>

                <button class="module-btn" onclick="openModule('debug-params-1')">
                    <i class="fas fa-cogs module-icon"></i>
                    <span class="module-text">调试参数1</span>
                </button>

                <button class="module-btn" onclick="openModule('debug-params-2')">
                    <i class="fas fa-tools module-icon"></i>
                    <span class="module-text">调试参数2</span>
                </button>

                <button class="module-btn" onclick="openModule('history-event')">
                    <i class="fas fa-calendar-alt module-icon"></i>
                    <span class="module-text">历史事件</span>
                </button>

                <!-- 第三行 -->
                <button class="module-btn" onclick="openModule('parameter-curve')">
                    <i class="fas fa-chart-area module-icon"></i>
                    <span class="module-text">参数曲线</span>
                </button>

                <button class="module-btn" onclick="openModule('dsp')">
                    <i class="fas fa-microchip module-icon"></i>
                    <span class="module-text">DSP</span>
                </button>

                <button class="module-btn" onclick="openModule('fault-wave')">
                    <i class="fas fa-wave-square module-icon"></i>
                    <span class="module-text">故障录波</span>
                </button>

                <button class="module-btn" onclick="openModule('version-info')">
                    <i class="fas fa-info-circle module-icon"></i>
                    <span class="module-text">版本信息</span>
                </button>
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="footer-left">
                <span>系统版本: v2.1.0 | 最后更新: 2025-06-24 14:30:25</span>
            </div>
            <div class="footer-right">
                <span>© 2025 桂林智源 - SVG 数字化系统</span>
            </div>
        </footer>
    </div>

    <!-- 功能模块弹窗 -->
    <div id="moduleModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-controls">
                    <button class="modal-btn" onclick="goBack()" title="返回上一页">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="modal-btn" onclick="closeModal()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-content">
                <iframe id="moduleIframe" class="modal-iframe" src=""></iframe>
            </div>
        </div>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本 JavaScript
         * 功能模块管理和弹窗控制
         */

        // 模块配置映射
        const moduleConfig = {
            'electrical-topology': {
                title: '电气系统拓扑图',
                icon: 'fas fa-bolt',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date='
            },
            'cooling-topology': {
                title: '水冷系统拓扑图',
                icon: 'fas fa-tint',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date='
            },
            'io-status': {
                title: 'I/O状态监控',
                icon: 'fas fa-plug',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date='
            },
            'unit-status': {
                title: '单元状态监控',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&wework_cfm_code=NOhs%2BuVWHo97Du860XjXIPC5tyE8DwbeJo3xoLtc8tn94QGaXR9LJW5VvFSCVJID5Fpwj6f%2FVjFU6LVqrXItFhckh8qcSUUqInRO3%2Fb3FD0Ee6bfED2vOqLVG6i2ymNIFQ7%2Fi%2BDxy8I7Xi5S1uPRzyxBjWWrv5w9p218BH1F2vIV&date='
            },
            'master-control': {
                title: '主控|辅控',
                icon: 'fas fa-sitemap',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f1379ce-d7b5-4017-9d9d-78d49813cd8c&type=3&date='
            },
            'debug-params-1': {
                title: '调试参数1',
                icon: 'fas fa-cogs',
                url: '调试参数1.html'
            },
            'debug-params-2': {
                title: '调试参数2',
                icon: 'fas fa-tools',
                url: '调试参数2.html'
            },
            'history-event': {
                title: '历史事件',
                icon: 'fas fa-calendar-alt',
                url: '历史事件.html'
            },
            'parameter-curve': {
                title: '参数曲线',
                icon: 'fas fa-chart-area',
                url: '参数曲线.html'
            },
            'dsp': {
                title: 'DSP',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date='
            },
            'fault-wave': {
                title: '故障录波',
                icon: 'fas fa-wave-square',
                url: '故障录波.html'
            },
            'version-info': {
                title: '版本信息',
                icon: 'fas fa-info-circle',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=eb900ac1-737c-4610-b4b3-ea05239531e3&type=3&date='
            }
        };

        // 全局变量：跟踪iframe导航状态
        let iframeNavigationStack = [];
        let iframeInitialUrl = '';

        /**
         * 全局函数：供iframe内页面调用，记录导航状态
         * @param {string} url - 目标URL
         * @param {string} title - 页面标题
         */
        window.recordIframeNavigation = function(url, title) {
            console.log('iframe内页面请求记录导航:', title, url.substring(0, 50) + '...');
            iframeNavigationStack.push({
                url: url,
                timestamp: Date.now(),
                title: title || '页面导航'
            });
            console.log('导航栈已更新，当前深度:', iframeNavigationStack.length);
        };

        /**
         * 打开功能模块
         * @param {string} moduleId - 模块ID
         */
        function openModule(moduleId) {
            console.log(`打开模块: ${moduleId}`);

            const config = moduleConfig[moduleId];
            if (!config) {
                console.error(`未找到模块配置: ${moduleId}`);
                return;
            }

            // 获取弹窗元素
            const moduleIframe = document.getElementById('moduleIframe');
            const modalOverlay = document.getElementById('moduleModal');

            // 构建URL（如果需要添加时间戳）
            let url = config.url;
            if (url.includes('mqtt.qizhiyun.cc')) {
                url += new Date().getTime();
            }

            // 重置导航状态跟踪
            iframeNavigationStack = [];
            iframeInitialUrl = url;
            console.log('重置iframe导航状态跟踪，初始URL:', url);

            // 清除iframe历史记录：先设置为空白页，确保iframe重置
            console.log('清除iframe历史记录');
            moduleIframe.src = 'about:blank';

            // 记录加载时间用于初始状态检测
            moduleIframe.setAttribute('data-load-time', Date.now().toString());

            // 短暂延迟后设置目标URL，确保清除操作完成
            setTimeout(() => {
                moduleIframe.src = url;
                // 更新加载时间
                moduleIframe.setAttribute('data-load-time', Date.now().toString());

                // 记录初始页面到导航栈
                iframeNavigationStack.push({
                    url: url,
                    timestamp: Date.now(),
                    title: config.title
                });

                // 添加iframe加载监听器，用于跟踪页面导航
                setupIframeNavigationTracking(moduleIframe);

                console.log(`iframe已设置新URL: ${url}`);
                console.log('导航栈状态:', iframeNavigationStack);
            }, 50);

            // 显示弹窗
            modalOverlay.classList.add('show');

            // 添加触摸屏优化的事件监听
            addTouchOptimization();

            console.log(`已打开模块: ${config.title}`);
        }

        /**
         * 设置iframe导航跟踪
         * @param {HTMLIFrameElement} iframe - iframe元素
         */
        function setupIframeNavigationTracking(iframe) {
            // 移除之前的监听器（如果存在）
            if (iframe._navigationTracker) {
                iframe.removeEventListener('load', iframe._navigationTracker);
            }

            // 创建新的导航跟踪器
            iframe._navigationTracker = function() {
                try {
                    const currentUrl = iframe.contentWindow.location.href;
                    const lastStackItem = iframeNavigationStack[iframeNavigationStack.length - 1];

                    // 如果URL发生变化，且不是初始加载，则记录到导航栈
                    if (lastStackItem && currentUrl !== lastStackItem.url &&
                        currentUrl !== 'about:blank' && currentUrl !== '') {

                        console.log('检测到iframe页面导航:');
                        console.log('- 从:', lastStackItem.url.substring(0, 50) + '...');
                        console.log('- 到:', currentUrl.substring(0, 50) + '...');

                        // 添加新页面到导航栈
                        iframeNavigationStack.push({
                            url: currentUrl,
                            timestamp: Date.now(),
                            title: '页面导航'
                        });

                        console.log('导航栈已更新，当前深度:', iframeNavigationStack.length);
                    }
                } catch (error) {
                    // 跨域情况下无法访问URL，这是正常的
                    console.log('无法跟踪iframe导航（可能跨域）:', error.message);
                }
            };

            // 添加加载事件监听器
            iframe.addEventListener('load', iframe._navigationTracker);
        }

        /**
         * 关闭弹窗
         */
        function closeModal() {
            console.log('关闭弹窗');
            const modalOverlay = document.getElementById('moduleModal');
            const moduleIframe = document.getElementById('moduleIframe');

            modalOverlay.classList.remove('show');

            // 清理导航跟踪
            if (moduleIframe._navigationTracker) {
                moduleIframe.removeEventListener('load', moduleIframe._navigationTracker);
                moduleIframe._navigationTracker = null;
            }

            // 重置导航状态
            iframeNavigationStack = [];
            iframeInitialUrl = '';

            // 清空iframe源以释放资源
            setTimeout(() => {
                moduleIframe.src = '';
            }, 300);
        }

        /**
         * 智能返回功能：检测iframe历史记录状态并执行相应操作
         * 修复版本：增强历史记录检测逻辑，防止异常跳转
         */
        function goBack() {
            console.log('执行智能返回功能');
            const moduleIframe = document.getElementById('moduleIframe');

            if (!moduleIframe || !moduleIframe.src || moduleIframe.src === 'about:blank') {
                console.log('iframe未加载有效内容，直接关闭弹窗');
                closeModal();
                return;
            }

            try {
                // 尝试检测iframe是否有有效的历史记录
                const canGoBack = checkIframeHistoryFixed(moduleIframe);

                if (canGoBack) {
                    console.log('检测到iframe有效历史记录，执行返回上一页');
                    performSafeGoBack(moduleIframe);
                } else {
                    console.log('iframe没有有效历史记录，关闭弹窗');
                    closeModal();
                }
            } catch (error) {
                console.warn('无法访问iframe历史记录，可能是跨域限制:', error.message);
                // 跨域限制时，检查导航栈
                if (iframeNavigationStack.length > 1) {
                    console.log('跨域环境下使用导航栈执行返回');
                    performNavigationStackGoBack(moduleIframe);
                } else {
                    console.log('跨域环境下且无导航记录，关闭弹窗');
                    closeModal();
                }
            }
        }

        /**
         * 修复版本：检测iframe是否有有效的历史记录可以返回
         * @param {HTMLIFrameElement} iframe - iframe元素
         * @returns {boolean} - 是否可以返回上一页
         */
        function checkIframeHistoryFixed(iframe) {
            try {
                const iframeWindow = iframe.contentWindow;

                // 检查iframe是否刚刚加载或处于初始状态
                if (isIframeInInitialState(iframe)) {
                    console.log('iframe处于初始状态，无有效历史记录');
                    return false;
                }

                // 优先使用导航栈检测
                if (iframeNavigationStack.length > 1) {
                    console.log(`导航栈检测：发现${iframeNavigationStack.length}个历史记录`);
                    console.log('导航栈内容:', iframeNavigationStack.map(item => ({
                        url: item.url.substring(0, 50) + '...',
                        title: item.title
                    })));
                    return true;
                }

                // 检测当前URL是否与初始URL不同（表示发生了导航）
                try {
                    const currentUrl = iframeWindow.location.href;
                    if (currentUrl !== iframeInitialUrl && iframeInitialUrl) {
                        console.log('URL变化检测：发现页面导航');
                        console.log('初始URL:', iframeInitialUrl.substring(0, 50) + '...');
                        console.log('当前URL:', currentUrl.substring(0, 50) + '...');

                        // 动态更新导航栈
                        if (iframeNavigationStack.length === 0 ||
                            iframeNavigationStack[iframeNavigationStack.length - 1].url !== currentUrl) {
                            iframeNavigationStack.push({
                                url: currentUrl,
                                timestamp: Date.now(),
                                title: '页面导航'
                            });
                            console.log('已更新导航栈，当前长度:', iframeNavigationStack.length);
                        }
                        return iframeNavigationStack.length > 1;
                    }
                } catch (urlError) {
                    console.log('无法访问iframe URL（可能跨域），使用备用检测方法');
                }

                // 尝试访问iframe的history对象
                const iframeHistory = iframeWindow.history;

                // 检查是否可以访问history.length
                if (typeof iframeHistory.length === 'number') {
                    console.log(`iframe历史记录长度: ${iframeHistory.length}`);

                    // 修复：放宽history.length的判断条件
                    // 对于外部网站，history.length可能很大，这是正常的
                    if (iframeHistory.length > 50) {
                        console.log('检测到外部网站的全局history，使用导航栈判断');
                        // 即使history.length很大，如果我们的导航栈显示有导航，仍然可以返回
                        return iframeNavigationStack.length > 0;
                    }

                    // 正常情况下，history.length > 1 表示有历史记录可以返回
                    const hasHistory = iframeHistory.length > 1;
                    console.log('history.length检测结果:', hasHistory);
                    return hasHistory;
                }

                // 如果无法获取length，尝试其他方法
                return checkAlternativeHistoryFixed(iframeWindow);

            } catch (error) {
                console.warn('无法直接检测iframe历史记录:', error.message);
                // 跨域情况下，如果导航栈有记录，仍然可以返回
                if (iframeNavigationStack.length > 1) {
                    console.log('跨域环境下使用导航栈判断，可以返回');
                    return true;
                }
                // 抛出异常让上层处理跨域情况
                throw error;
            }
        }

        /**
         * 修复版本：替代方法检测iframe历史记录
         * @param {Window} iframeWindow - iframe的window对象
         * @returns {boolean} - 是否可能有历史记录
         */
        function checkAlternativeHistoryFixed(iframeWindow) {
            try {
                // 更保守的检测方法：只有在明确检测到页面内导航时才返回true
                const currentUrl = iframeWindow.location.href;
                const iframeSrc = document.getElementById('moduleIframe').src;
                
                // 检查URL是否有明显的页面间导航标识
                const hasPageNavigation = checkPageNavigationIndicators(currentUrl, iframeSrc);
                
                if (hasPageNavigation) {
                    console.log('检测到页面内导航，可能有历史记录');
                    return true;
                }
                
                console.log('未检测到明确的页面间导航，认为无有效历史记录');
                return false;
                
            } catch (error) {
                console.warn('替代检测方法失败:', error.message);
                return false;
            }
        }

        /**
         * 检查iframe是否处于初始状态
         * @param {HTMLIFrameElement} iframe - iframe元素
         * @returns {boolean} - 是否处于初始状态
         */
        function isIframeInInitialState(iframe) {
            try {
                // 检查iframe的src是否是about:blank或为空
                if (!iframe.src || iframe.src === 'about:blank' || iframe.src === '') {
                    return true;
                }
                
                // 检查iframe是否刚刚被设置（在最近500ms内）
                const iframe_load_time = iframe.getAttribute('data-load-time');
                if (iframe_load_time) {
                    const loadTime = parseInt(iframe_load_time);
                    const timeSinceLoad = Date.now() - loadTime;
                    if (timeSinceLoad < 500) {
                        console.log('（阶段性检测）iframe刚刚加载，处于初始状态');
                        return true;
                    }
                }
                
                return false;
            } catch (error) {
                console.warn('检查iframe初始状态时出错:', error.message);
                return false;
            }
        }
        
        /**
         * 检查页面导航指示器
         * @param {string} currentUrl - 当前URL
         * @param {string} initialUrl - 初始URL
         * @returns {boolean} - 是否有页面导航
         */
        function checkPageNavigationIndicators(currentUrl, initialUrl) {
            try {
                // 去除URL中的时间戳和查询参数进行比较
                const cleanCurrentUrl = currentUrl.split('?')[0].split('#')[0];
                const cleanInitialUrl = initialUrl.split('?')[0].split('#')[0];
                
                // 如果基础URL不同，认为有页面导航
                if (cleanCurrentUrl !== cleanInitialUrl) {
                    console.log('检测到URL路径变化:', {
                        initial: cleanInitialUrl,
                        current: cleanCurrentUrl
                    });
                    return true;
                }
                
                // 检查URL中是否包含典型的页面导航参数
                const navigationIndicators = ['page=', 'tab=', 'view=', 'section=', '#'];
                const hasNavigationParams = navigationIndicators.some(indicator => 
                    currentUrl.includes(indicator) && !initialUrl.includes(indicator)
                );
                
                if (hasNavigationParams) {
                    console.log('检测到页面内导航参数变化');
                    return true;
                }
                
                return false;
            } catch (error) {
                console.warn('检查页面导航指示器时出错:', error.message);
                return false;
            }
        }
        
        /**
         * 使用导航栈执行返回操作
         * @param {HTMLIFrameElement} iframe - iframe元素
         */
        function performNavigationStackGoBack(iframe) {
            try {
                if (iframeNavigationStack.length <= 1) {
                    console.log('导航栈中没有足够的历史记录，关闭弹窗');
                    closeModal();
                    return;
                }

                // 移除当前页面（栈顶）
                const currentPage = iframeNavigationStack.pop();
                const previousPage = iframeNavigationStack[iframeNavigationStack.length - 1];

                console.log('导航栈返回操作:');
                console.log('- 当前页面:', currentPage.title, currentPage.url.substring(0, 50) + '...');
                console.log('- 返回到:', previousPage.title, previousPage.url.substring(0, 50) + '...');
                console.log('- 剩余栈深度:', iframeNavigationStack.length);

                // 直接导航到上一个页面
                iframe.src = previousPage.url;

                // 更新加载时间
                iframe.setAttribute('data-load-time', Date.now().toString());

                console.log('导航栈返回操作完成');

            } catch (error) {
                console.warn('导航栈返回操作失败:', error.message);
                console.log('返回操作失败，关闭弹窗');
                closeModal();
            }
        }

        /**
         * 安全地执行返回操作
         * @param {HTMLIFrameElement} iframe - iframe元素
         */
        function performSafeGoBack(iframe) {
            try {
                // 如果有导航栈记录，优先使用导航栈
                if (iframeNavigationStack.length > 1) {
                    console.log('检测到导航栈记录，使用导航栈执行返回');
                    performNavigationStackGoBack(iframe);
                    return;
                }

                // 记录尝试返回的时间
                const attemptTime = Date.now();
                let originalUrl = '';

                try {
                    originalUrl = iframe.contentWindow.location.href;
                } catch (urlError) {
                    console.log('无法获取原始URL（跨域），直接执行history.back()');
                }

                console.log('尝试执行安全返回操作');
                iframe.contentWindow.history.back();

                // 设置超时检测：如果页面没有变化，则认为没有历史记录
                setTimeout(() => {
                    checkGoBackResult(iframe, originalUrl, attemptTime);
                }, 800); // 缩短检测时间到800ms

            } catch (error) {
                console.warn('执行返回操作失败:', error.message);
                // 如果标准返回失败，尝试使用导航栈
                if (iframeNavigationStack.length > 1) {
                    console.log('标准返回失败，尝试使用导航栈');
                    performNavigationStackGoBack(iframe);
                } else {
                    console.log('返回操作失败，关闭弹窗');
                    closeModal();
                }
            }
        }
        
        /**
         * 检查返回操作的结果
         * @param {HTMLIFrameElement} iframe - iframe元素
         * @param {string} originalUrl - 原始URL
         * @param {number} attemptTime - 尝试返回的时间戳
         */
        function checkGoBackResult(iframe, originalUrl, attemptTime) {
            try {
                if (!iframe || !iframe.contentWindow) {
                    console.log('iframe已被销毁，停止检测');
                    return;
                }

                const modalIsVisible = document.getElementById('moduleModal').classList.contains('show');

                if (!modalIsVisible) {
                    console.log('弹窗已关闭，停止检测');
                    return;
                }

                let currentUrl = '';
                let urlAccessible = true;

                try {
                    currentUrl = iframe.contentWindow.location.href;
                } catch (urlError) {
                    console.log('无法访问当前URL（跨域），使用导航栈判断返回结果');
                    urlAccessible = false;
                }

                // 如果可以访问URL且URL没有变化，可能是没有历史记录
                if (urlAccessible && currentUrl === originalUrl && originalUrl) {
                    console.log('返回操作可能无效（URL未变化），尝试使用导航栈返回');
                    // 如果标准返回失败，尝试使用导航栈
                    if (iframeNavigationStack.length > 1) {
                        console.log('使用导航栈执行备用返回操作');
                        performNavigationStackGoBack(iframe);
                    } else {
                        console.log('导航栈也无历史记录，关闭弹窗');
                        closeModal();
                    }
                } else if (urlAccessible && currentUrl !== originalUrl) {
                    console.log('返回操作成功，URL已变化，继续显示iframe');
                    // URL变化成功，更新导航栈（移除当前页面）
                    if (iframeNavigationStack.length > 1) {
                        iframeNavigationStack.pop();
                        console.log('已更新导航栈，当前深度:', iframeNavigationStack.length);
                    }
                } else if (!urlAccessible) {
                    // 跨域情况下，假设返回操作成功
                    console.log('跨域环境下假设返回操作成功，更新导航栈');
                    if (iframeNavigationStack.length > 1) {
                        iframeNavigationStack.pop();
                        console.log('已更新导航栈，当前深度:', iframeNavigationStack.length);
                    }
                }

            } catch (error) {
                console.warn('检查返回结果时出错:', error.message);
                // 出错时，如果有导航栈记录，尝试使用导航栈返回
                if (iframeNavigationStack.length > 1) {
                    console.log('检测出错，尝试使用导航栈返回');
                    performNavigationStackGoBack(iframe);
                } else {
                    console.log('检测出错且无导航记录，保持当前状态');
                }
            }
        }



        /**
         * 添加触摸屏优化
         */
        function addTouchOptimization() {
            // 防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 防止长按选择文本
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
            });
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            const timeDisplay = document.getElementById('currentTime');
            if (timeDisplay) {
                timeDisplay.textContent = timeString;
            }
        }

        /**
         * 键盘事件处理（ESC键关闭弹窗）
         */
        function handleKeyboard(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        }

        /**
         * 初始化页面
         */
        function initializePage() {
            console.log('初始化触摸屏版本页面');

            // 更新时间显示
            updateTime();
            setInterval(updateTime, 1000);

            // 添加键盘事件监听
            document.addEventListener('keydown', handleKeyboard);

            // 添加触摸屏优化
            addTouchOptimization();

            // 点击弹窗外部关闭弹窗
            const modalOverlay = document.getElementById('moduleModal');
            modalOverlay.addEventListener('click', function(event) {
                if (event.target === modalOverlay) {
                    closeModal();
                }
            });

            console.log('触摸屏版本页面初始化完成');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                updateTime();
            }
        });
    </script>
</body>
</html>