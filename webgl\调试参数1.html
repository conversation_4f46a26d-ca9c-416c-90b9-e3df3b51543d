<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>调试参数1 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            width: 100vw;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
        }

        /* 科技感背景动画 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            background-attachment: fixed;
        }

        .container {
            width: 100%;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 48px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .header p {
            font-size: 24px;
            color: #b8c5d6;
            opacity: 0.8;
        }

        .params-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 30px;
            flex: 1;
            padding: 0 40px;
        }

        .param-card {
            background: linear-gradient(135deg, rgba(26, 31, 46, 0.9), rgba(42, 49, 66, 0.9));
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            min-height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .param-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .param-card:hover::before {
            left: 100%;
        }

        .param-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: #00d4ff;
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
        }

        .param-card:active {
            transform: translateY(-5px) scale(0.98);
        }

        .param-icon {
            font-size: 56px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .param-title {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 0;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            border-top: 1px solid rgba(0, 212, 255, 0.3);
        }

        .footer p {
            color: #7a8ba0;
            font-size: 16px;
        }

        /* 响应式调整 */
        @media (min-width: 1920px) {
            .params-grid {
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: repeat(2, 1fr);
                gap: 30px;
            }
            
            .param-card {
                padding: 30px;
                min-height: 280px;
            }
            
            .param-icon {
                font-size: 56px;
            }
            
            .param-title {
                font-size: 32px;
            }
        }
        
        @media (max-width: 1919px) and (min-width: 1601px) {
            .params-grid {
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: repeat(2, 1fr);
                gap: 25px;
            }
            
            .param-card {
                padding: 25px;
                min-height: 260px;
            }
            
            .param-icon {
                font-size: 50px;
            }
            
            .param-title {
                font-size: 30px;
            }
        }
        
        @media (max-width: 1600px) and (min-width: 1367px) {
            .params-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 25px;
            }
            
            .param-card {
                padding: 25px;
                min-height: 240px;
            }
            
            .param-icon {
                font-size: 48px;
            }
            
            .param-title {
                font-size: 28px;
            }
        }
        
        @media (max-width: 1366px) {
            .params-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .param-card {
                padding: 20px;
                min-height: 220px;
            }
            
            .param-icon {
                font-size: 42px;
                margin-bottom: 15px;
            }
            
            .param-title {
                font-size: 24px;
            }
            
            .header {
                margin-bottom: 30px;
            }
            
            .header h1 {
                font-size: 42px;
            }
            
            .header p {
                font-size: 20px;
            }
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .param-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .param-card:nth-child(1) { animation-delay: 0.1s; }
        .param-card:nth-child(2) { animation-delay: 0.2s; }
        .param-card:nth-child(3) { animation-delay: 0.3s; }
        .param-card:nth-child(4) { animation-delay: 0.4s; }
        .param-card:nth-child(5) { animation-delay: 0.5s; }
        .param-card:nth-child(6) { animation-delay: 0.6s; }
        .param-card:nth-child(7) { animation-delay: 0.7s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cogs"></i> 调试参数1</h1>
            <p>系统调试与参数配置管理界面</p>
        </div>

        <div class="params-grid">
            <div class="param-card" onclick="openDeviceOperation()">
                <div class="param-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <div class="param-title">设备操作</div>
            </div>

            <div class="param-card" onclick="openControlMode()">
                <div class="param-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div class="param-title">控制模式</div>
            </div>

            <div class="param-card" onclick="openSystemParams()">
                <div class="param-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="param-title">系统参数</div>
            </div>

            <div class="param-card" onclick="openProtectionEnable()">
                <div class="param-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="param-title">保护使能</div>
            </div>

            <div class="param-card" onclick="openProtectionParams()">
                <div class="param-icon">
                    <i class="fas fa-shield-virus"></i>
                </div>
                <div class="param-title">保护参数</div>
            </div>

            <div class="param-card" onclick="openFilterControl()">
                <div class="param-icon">
                    <i class="fas fa-filter"></i>
                </div>
                <div class="param-title">滤波控制</div>
            </div>

            <div class="param-card" onclick="openUnitInfo()">
                <div class="param-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="param-title">单元信息</div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 桂林智源 - SVG 数字化系统 调试参数1界面</p>
        </div>
    </div>

    <script>
        /**
         * 设备操作
         */
        function openDeviceOperation() {
            console.log('打开设备操作页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=32f78f37-5ce9-4c17-9d09-43387b010059&type=3';
        }

        /**
         * 控制模式
         */
        function openControlMode() {
            console.log('打开控制模式页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=5060e41a-68cf-47b1-b2b0-9989f9d581c2&type=3';
        }

        /**
         * 系统参数
         */
        function openSystemParams() {
            console.log('打开系统参数页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=8e70b95b-8d36-4fba-806b-ae66d078741a&type=3';
        }

        /**
         * 保护使能
         */
        function openProtectionEnable() {
            console.log('打开保护使能页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=76df28ab-e08a-4788-9394-e411235f4806&type=3';
        }

        /**
         * 保护参数
         */
        function openProtectionParams() {
            console.log('打开保护参数页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=a3ae2543-ee36-4713-8dbe-2db34d1db4d2&type=3';
        }

        /**
         * 滤波控制
         */
        function openFilterControl() {
            console.log('打开滤波控制页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=1ef97cf9-ab3f-4c96-a2f3-60c327bc3e0b&type=3';
        }

        /**
         * 单元信息
         */
        function openUnitInfo() {
            console.log('打开单元信息页面');
            window.location.href = 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=f28b4a6a-eab6-47c8-a710-e167e983a8aa&type=3';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试参数1页面加载完成');
        });
    </script>
</body>
</html>
